import { Schema, model, Document } from 'mongoose';

export interface ICrosscore extends Document {
  userId: string;
  requestId: string;
  verificationType: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  requestData: Record<string, {}>;
  responseData?: Record<string, {}>;
  experianRequestId?: string;
  experianStatus?: string;
  errorMessage?: string;
  webhookReceived: boolean;
  webhookData?: Record<string, {}>;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

const crosscoreSchema = new Schema<ICrosscore>(
  {
    userId: {
      type: String,
      required: true,
      index: true,
    },
    requestId: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    verificationType: {
      type: String,
      required: true,
      enum: [
        'identity_verification',
        'document_verification',
        'address_verification',
        'credit_check',
        'fraud_check',
        'kyc_verification',
      ],
    },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'in_progress', 'completed', 'failed', 'cancelled'],
      default: 'pending',
    },
    requestData: {
      type: Schema.Types.Mixed,
      required: true,
    },
    responseData: {
      type: Schema.Types.Mixed,
      required: false,
    },
    experianRequestId: {
      type: String,
      required: false,
      index: true,
    },
    experianStatus: {
      type: String,
      required: false,
    },
    errorMessage: {
      type: String,
      required: false,
      maxlength: 1000,
    },
    webhookReceived: {
      type: Boolean,
      required: true,
      default: false,
    },
    webhookData: {
      type: Schema.Types.Mixed,
      required: false,
    },
    completedAt: {
      type: Date,
      required: false,
    },
  },
  {
    timestamps: true,
    collection: 'crosscoreRequests',
  }
);

// Indexes for better query performance
crosscoreSchema.index({ userId: 1, status: 1 });
crosscoreSchema.index({ createdAt: -1 });
crosscoreSchema.index({ status: 1, verificationType: 1 });
crosscoreSchema.index({ experianRequestId: 1 });

export const Crosscore = model<ICrosscore>('Crosscore', crosscoreSchema);
