import { Server } from '@hapi/hapi';
import { WebhookController } from './webhook.controller';
import { API_CONSTANTS } from '../common/constants';

export async function registerWebhookRoutes(server: Server): Promise<void> {
  // Initialize controller
  const webhookController = new WebhookController();

  // Webhook routes
  server.route([
    {
      method: 'POST',
      path: `${API_CONSTANTS.WEBHOOK_PATH}/crosscore`,
      handler: webhookController.handleCrosscoreWebhook.bind(webhookController),
      options: {
        description: 'Handle CrossCore webhooks',
        tags: ['api', 'webhooks', 'crosscore'],
      },
    },
    {
      method: 'POST',
      path: `${API_CONSTANTS.WEBHOOK_PATH}/generic`,
      handler: webhookController.handleGenericWebhook.bind(webhookController),
      options: {
        description: 'Handle generic webhooks',
        tags: ['api', 'webhooks'],
      },
    },
    {
      method: 'GET',
      path: `${API_CONSTANTS.WEBHOOK_PATH}/health`,
      handler: webhookController.healthCheck.bind(webhookController),
      options: {
        description: 'Webhook service health check',
        tags: ['api', 'webhooks', 'health'],
      },
    },
    {
      method: ['GET', 'POST'],
      path: `${API_CONSTANTS.WEBHOOK_PATH}/validate`,
      handler: webhookController.validateWebhook.bind(webhookController),
      options: {
        description: 'Validate webhook endpoint',
        tags: ['api', 'webhooks'],
      },
    },
  ]);

  console.log('Webhook routes registered');
}
