import { Request, ResponseToolkit, ResponseObject } from '@hapi/hapi';
import { CrosscoreService } from '../crosscore/crosscore.service';
import { UserIncidentService } from '../userIncident/userIncident.service';
import {
  crosscoreWebhookSchema,
  CrosscoreWebhookPayload,
} from '../crosscore/crosscore.dto';
import Jo<PERSON> from '@hapi/joi';

export class WebhookController {
  private crosscoreService: CrosscoreService;
  private userIncidentService: UserIncidentService;

  constructor() {
    this.crosscoreService = new CrosscoreService();
    this.userIncidentService = new UserIncidentService();
  }

  /**
   * Handles incoming CrossCore/Experian webhooks
   */
  async handleCrosscoreWebhook(
    request: Request,
    h: ResponseToolkit
  ): Promise<ResponseObject> {
    try {
      // Validate webhook payload
      const { error, value } = crosscoreWebhookSchema.validate(request.payload);
      if (error) {
        console.error('Invalid webhook payload:', error.details[0].message);
        return h.response({ error: 'Invalid webhook payload' }).code(400);
      }

      const webhookPayload = value as CrosscoreWebhookPayload;

      // Log incoming webhook for debugging
      console.log('Received CrossCore webhook:', {
        requestId: webhookPayload.requestId,
        experianRequestId: webhookPayload.experianRequestId,
        status: webhookPayload.status,
        timestamp: webhookPayload.timestamp,
      });

      // Process the webhook through CrossCore service
      const result = await this.crosscoreService.processWebhook(webhookPayload);

      if (!result) {
        console.error('Failed to process webhook - request not found');
        return h.response({ error: 'Request not found' }).code(404);
      }

      // If the verification failed, create an incident
      if (result.status === 'failed') {
        await this.createIncidentForFailedVerification(result);
      }

      console.log('Webhook processed successfully:', result.id);
      return h
        .response({
          message: 'Webhook processed successfully',
          requestId: result.requestId,
        })
        .code(200);
    } catch (error) {
      console.error('Error processing CrossCore webhook:', error);
      return h.response({ error: 'Internal server error' }).code(500);
    }
  }

  /**
   * Generic webhook handler for other types of webhooks
   */
  async handleGenericWebhook(
    request: Request,
    h: ResponseToolkit
  ): Promise<ResponseObject> {
    try {
      const payload = request.payload as {};
      const headers = request.headers;

      // Log the webhook for debugging
      console.log('Received generic webhook:', {
        headers: headers,
        payload: payload,
        timestamp: new Date().toISOString(),
      });

      // Basic validation
      if (!payload || typeof payload !== 'object') {
        return h.response({ error: 'Invalid payload' }).code(400);
      }

      // Here you can add logic to handle different types of webhooks
      // based on headers or payload structure

      return h
        .response({
          message: 'Webhook received',
          timestamp: new Date().toISOString(),
        })
        .code(200);
    } catch (error) {
      console.error('Error processing generic webhook:', error);
      return h.response({ error: 'Internal server error' }).code(500);
    }
  }

  /**
   * Health check endpoint for webhook service
   */
  async healthCheck(
    request: Request,
    h: ResponseToolkit
  ): Promise<ResponseObject> {
    return h
      .response({
        status: 'healthy',
        service: 'webhook-service',
        timestamp: new Date().toISOString(),
      })
      .code(200);
  }

  /**
   * Webhook validation endpoint (for webhook setup verification)
   */
  async validateWebhook(
    request: Request,
    h: ResponseToolkit
  ): Promise<ResponseObject> {
    try {
      // Check for validation challenge (common in webhook setups)
      const challenge =
        request.query.challenge || (request.payload as {})?.challenge;

      if (challenge) {
        console.log('Webhook validation challenge received:', challenge);
        return h.response({ challenge }).code(200);
      }

      // Verify webhook signature if provided
      const signature = request.headers['x-webhook-signature'];
      if (signature) {
        // Here you would verify the signature against your webhook secret
        // For now, we'll just log it
        console.log('Webhook signature received:', signature);
      }

      return h
        .response({
          message: 'Webhook endpoint validated',
          timestamp: new Date().toISOString(),
        })
        .code(200);
    } catch (error) {
      console.error('Error validating webhook:', error);
      return h.response({ error: 'Validation failed' }).code(500);
    }
  }

  /**
   * Creates an incident when a verification fails
   */
  private async createIncidentForFailedVerification(
    crosscoreResult: {}
  ): Promise<void> {
    try {
      const incidentData = {
        userId: crosscoreResult.userId,
        incidentType: this.mapVerificationTypeToIncidentType(
          crosscoreResult.verificationType
        ),
        description: `Verification failed for ${crosscoreResult.verificationType}. Error: ${crosscoreResult.errorMessage || 'Unknown error'}`,
        priority: 'high' as const,
        metadata: {
          crosscoreRequestId: crosscoreResult.requestId,
          experianRequestId: crosscoreResult.experianRequestId,
          verificationType: crosscoreResult.verificationType,
          failureReason: crosscoreResult.errorMessage,
          webhookData: crosscoreResult.webhookData,
        },
      };

      await this.userIncidentService.createIncident(incidentData);
      console.log(
        `Created incident for failed verification: ${crosscoreResult.requestId}`
      );
    } catch (error) {
      console.error(
        'Failed to create incident for failed verification:',
        error
      );
    }
  }

  /**
   * Maps CrossCore verification types to incident types
   */
  private mapVerificationTypeToIncidentType(verificationType: string): string {
    const mapping: Record<string, string> = {
      identity_verification: 'identity_verification',
      document_verification: 'document_verification',
      address_verification: 'address_verification',
      credit_check: 'compliance_check',
      fraud_check: 'fraud_detection',
      kyc_verification: 'compliance_check',
    };

    return mapping[verificationType] || 'other';
  }
}
