import { Server } from '@hapi/hapi';
import { registerHealthRoutes } from './health/health.routes';
import { registerUserIncidentRoutes } from './userIncident/userIncident.routes';
import { registerCrosscoreRoutes } from './crosscore/crosscore.routes';
import { registerWebhookRoutes } from './webhook/webhook.routes';

export async function registerRoutes(server: Server): Promise<void> {
  // Register all route modules
  await registerHealthRoutes(server);
  await registerUserIncidentRoutes(server);
  // await registerCrosscoreRoutes(server);
  // await registerWebhookRoutes(server);

  console.log('Routes registered successfully');
}
