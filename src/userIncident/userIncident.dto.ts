import Joi from '@hapi/joi';

// Request DTOs
export interface CreateUserIncidentRequest {
  userId: string;
  incidentType: string;
  description: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  metadata?: Record<string, unknown>;
}

export interface UpdateUserIncidentRequest {
  incidentType?: string;
  description?: string;
  status?: 'pending' | 'in_progress' | 'resolved' | 'closed';
  priority?: 'low' | 'medium' | 'high' | 'critical';
  assignedTo?: string;
  metadata?: Record<string, unknown>;
}

export interface GetUserIncidentsQuery {
  userId?: string;
  status?: string;
  priority?: string;
  incidentType?: string;
  assignedTo?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Response DTOs
export interface UserIncidentResponse {
  id: string;
  userId: string;
  incidentType: string;
  description: string;
  status: 'pending' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedTo?: string;
  metadata?: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
}

export interface PaginatedUserIncidentsResponse {
  incidents: UserIncidentResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Validation schemas
export const createUserIncidentSchema = Joi.object({
  userId: Joi.string().required().min(1).max(100),
  incidentType: Joi.string()
    .required()
    .valid(
      'identity_verification',
      'document_verification',
      'address_verification',
      'fraud_detection',
      'compliance_check',
      'other'
    ),
  description: Joi.string().required().min(1).max(1000),
  priority: Joi.string()
    .optional()
    .valid('low', 'medium', 'high', 'critical')
    .default('medium'),
  metadata: Joi.object().optional(),
});

export const updateUserIncidentSchema = Joi.object({
  incidentType: Joi.string()
    .optional()
    .valid(
      'identity_verification',
      'document_verification',
      'address_verification',
      'fraud_detection',
      'compliance_check',
      'other'
    ),
  description: Joi.string().optional().min(1).max(1000),
  status: Joi.string()
    .optional()
    .valid('pending', 'in_progress', 'resolved', 'closed'),
  priority: Joi.string().optional().valid('low', 'medium', 'high', 'critical'),
  assignedTo: Joi.string().optional().allow('').max(100),
  metadata: Joi.object().optional(),
});

export const getUserIncidentsQuerySchema = Joi.object({
  userId: Joi.string().optional().min(1).max(100),
  status: Joi.string()
    .optional()
    .valid('pending', 'in_progress', 'resolved', 'closed'),
  priority: Joi.string().optional().valid('low', 'medium', 'high', 'critical'),
  incidentType: Joi.string()
    .optional()
    .valid(
      'identity_verification',
      'document_verification',
      'address_verification',
      'fraud_detection',
      'compliance_check',
      'other'
    ),
  assignedTo: Joi.string().optional().max(100),
  page: Joi.number().optional().min(1).default(1),
  limit: Joi.number().optional().min(1).max(100).default(10),
  sortBy: Joi.string()
    .optional()
    .valid('createdAt', 'updatedAt', 'priority', 'status')
    .default('createdAt'),
  sortOrder: Joi.string().optional().valid('asc', 'desc').default('desc'),
});
