import { Schema, model, Document } from 'mongoose';

export interface IUserIncident extends Document {
  userId: string;
  incidentType: string;
  description: string;
  status: 'pending' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedTo?: string;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
}

const userIncidentSchema = new Schema<IUserIncident>(
  {
    userId: {
      type: String,
      required: true,
      index: true,
    },
    incidentType: {
      type: String,
      required: true,
      enum: [
        'identity_verification',
        'document_verification',
        'address_verification',
        'fraud_detection',
        'compliance_check',
        'other',
      ],
    },
    description: {
      type: String,
      required: true,
      maxlength: 1000,
    },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'in_progress', 'resolved', 'closed'],
      default: 'pending',
    },
    priority: {
      type: String,
      required: true,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium',
    },
    assignedTo: {
      type: String,
      required: false,
    },
    metadata: {
      type: Schema.Types.Mixed,
      required: false,
    },
    resolvedAt: {
      type: Date,
      required: false,
    },
  },
  {
    timestamps: true,
    collection: 'userIncidents',
  }
);

// Indexes for better query performance
userIncidentSchema.index({ userId: 1, status: 1 });
userIncidentSchema.index({ createdAt: -1 });
userIncidentSchema.index({ status: 1, priority: 1 });

export const UserIncident = model<IUserIncident>(
  'UserIncident',
  userIncidentSchema
);
