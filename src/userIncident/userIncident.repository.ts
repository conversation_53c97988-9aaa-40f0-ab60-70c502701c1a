import { UserIncident, Prisma } from '../generated/prisma';
import { DatabaseConnection } from '../common/utils/database';
import { GetUserIncidentsQuery } from './userIncident.dto';

export class UserIncidentRepository {
  private prisma = DatabaseConnection.getInstance().getPrismaClient();

  /**
   * Creates a new user incident
   */
  async create(
    incidentData: Prisma.UserIncidentCreateInput
  ): Promise<UserIncident> {
    return await this.prisma.userIncident.create({
      data: incidentData,
    });
  }

  /**
   * Finds a user incident by ID
   */
  async findById(id: string): Promise<UserIncident | null> {
    return await this.prisma.userIncident.findUnique({
      where: { id },
    });
  }

  /**
   * Finds user incidents with filtering, pagination, and sorting
   */
  async findMany(
    query: GetUserIncidentsQuery
  ): Promise<{ incidents: UserIncident[]; total: number }> {
    const where: Prisma.UserIncidentWhereInput = {};

    // Build filter object
    if (query.userId) {
      where.userId = query.userId;
    }
    if (query.status) {
      where.status = query.status;
    }
    if (query.priority) {
      where.priority = query.priority;
    }
    if (query.incidentType) {
      where.incidentType = query.incidentType;
    }
    if (query.assignedTo) {
      where.assignedTo = query.assignedTo;
    }

    // Build sort object
    const sortField = query.sortBy || 'createdAt';
    const sortOrder = query.sortOrder === 'asc' ? 'asc' : 'desc';
    const orderBy: Prisma.UserIncidentOrderByWithRelationInput = {
      [sortField]: sortOrder,
    };

    // Pagination
    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    // Execute queries
    const [incidents, total] = await Promise.all([
      this.prisma.userIncident.findMany({
        where,
        orderBy,
        skip,
        take: limit,
      }),
      this.prisma.userIncident.count({ where }),
    ]);

    return { incidents, total };
  }

  /**
   * Finds user incidents by user ID
   */
  async findByUserId(userId: string): Promise<UserIncident[]> {
    return await this.prisma.userIncident.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Updates a user incident by ID
   */
  async updateById(
    id: string,
    updateData: Prisma.UserIncidentUpdateInput
  ): Promise<UserIncident | null> {
    try {
      return await this.prisma.userIncident.update({
        where: { id },
        data: updateData,
      });
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025'
      ) {
        return null; // Record not found
      }
      throw error;
    }
  }

  /**
   * Deletes a user incident by ID
   */
  async deleteById(id: string): Promise<UserIncident | null> {
    try {
      return await this.prisma.userIncident.delete({
        where: { id },
      });
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025'
      ) {
        return null; // Record not found
      }
      throw error;
    }
  }

  /**
   * Finds incidents by status
   */
  async findByStatus(
    status: 'pending' | 'in_progress' | 'resolved' | 'closed'
  ): Promise<UserIncident[]> {
    return await this.prisma.userIncident.findMany({
      where: { status },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Finds incidents assigned to a specific user
   */
  async findByAssignedTo(assignedTo: string): Promise<UserIncident[]> {
    return await this.prisma.userIncident.findMany({
      where: { assignedTo },
      orderBy: [{ priority: 'desc' }, { createdAt: 'desc' }],
    });
  }

  /**
   * Gets incident statistics
   */
  async getStatistics(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byPriority: Record<string, number>;
  }> {
    const [total, statusStats, priorityStats] = await Promise.all([
      this.prisma.userIncident.count(),
      this.prisma.userIncident.groupBy({
        by: ['status'],
        _count: { status: true },
      }),
      this.prisma.userIncident.groupBy({
        by: ['priority'],
        _count: { priority: true },
      }),
    ]);

    const byStatus: Record<string, number> = {};
    statusStats.forEach((stat: {}) => {
      byStatus[stat.status] = stat._count.status;
    });

    const byPriority: Record<string, number> = {};
    priorityStats.forEach((stat: {}) => {
      byPriority[stat.priority] = stat._count.priority;
    });

    return { total, byStatus, byPriority };
  }
}
