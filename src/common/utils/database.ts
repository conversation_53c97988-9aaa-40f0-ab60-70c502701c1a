import { PrismaClient } from '@prisma/client';
import { DATABASE_CONSTANTS } from '../constants';

export class DatabaseConnection {
  private static instance: DatabaseConnection;
  private prisma: PrismaClient;
  private isConnected: boolean = false;

  private constructor() {
    this.prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });
  }

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  /**
   * Gets the Prisma client instance
   */
  public getPrismaClient(): PrismaClient {
    return this.prisma;
  }

  /**
   * Connects to PostgreSQL database
   */
  public async connect(): Promise<void> {
    try {
      if (this.isConnected) {
        console.log('Database already connected');
        return;
      }

      // Test the connection
      await this.prisma.$connect();

      this.isConnected = true;
      console.log('Connected to PostgreSQL successfully');
    } catch (error) {
      console.error('Failed to connect to PostgreSQL:', error);
      this.isConnected = false;
      throw error;
    }
  }

  /**
   * Disconnects from PostgreSQL database
   */
  public async disconnect(): Promise<void> {
    try {
      if (!this.isConnected) {
        console.log('Database not connected');
        return;
      }

      await this.prisma.$disconnect();
      this.isConnected = false;
      console.log('Disconnected from PostgreSQL');
    } catch (error) {
      console.error('Error disconnecting from PostgreSQL:', error);
      throw error;
    }
  }

  /**
   * Checks if database is connected
   */
  public getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * Gets database connection info
   */
  public getConnectionInfo(): {
    isConnected: boolean;
    databaseUrl?: string;
  } {
    return {
      isConnected: this.isConnected,
      databaseUrl: process.env.DATABASE_URL ? '[REDACTED]' : undefined,
    };
  }

  /**
   * Performs health check on database connection
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    responseTime: number;
    error?: string;
  }> {
    const startTime = Date.now();

    try {
      if (!this.isConnected) {
        return {
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          error: 'Database not connected',
        };
      }

      // Perform a simple query to test the connection
      await this.prisma.$queryRaw`SELECT 1`;

      return {
        status: 'healthy',
        responseTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
