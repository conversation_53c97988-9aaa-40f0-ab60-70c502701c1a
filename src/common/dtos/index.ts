import { ApiResponseStatus, SortOrder } from '../enums';

// Base response interface
export interface BaseResponse {
  status: ApiResponseStatus;
  message?: string;
  timestamp: string;
}

// API Error response interface
export interface ErrorResponse extends BaseResponse {
  status: ApiResponseStatus.ERROR;
  error: {
    code: string;
    message: string;
    details?: unknown;
  };
}

// API Success response interface
export interface SuccessResponse<T = unknown> extends BaseResponse {
  status: ApiResponseStatus.SUCCESS;
  data: T;
}

// Pagination metadata interface
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// Paginated response interface
export interface PaginatedResponse<T> extends SuccessResponse<T[]> {
  pagination: PaginationMeta;
}

// Base query parameters interface
export interface BaseQueryParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: SortOrder;
}

// Date range query parameters interface
export interface DateRangeQueryParams {
  dateFrom?: string;
  dateTo?: string;
}

// Search query parameters interface
export interface SearchQueryParams {
  search?: string;
  searchFields?: string[];
}

// Filter query parameters interface
export interface FilterQueryParams {
  status?: string;
  priority?: string;
  type?: string;
  assignedTo?: string;
  userId?: string;
}

// Combined query parameters interface
export interface QueryParams
  extends BaseQueryParams,
    DateRangeQueryParams,
    SearchQueryParams,
    FilterQueryParams {}

// Health check response interface
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  service: string;
  version?: string;
  timestamp: string;
  uptime?: number;
  dependencies?: {
    [key: string]: {
      status: 'healthy' | 'unhealthy';
      responseTime?: number;
      error?: string;
    };
  };
}

// Statistics response interface
export interface StatisticsResponse {
  total: number;
  byStatus: Record<string, number>;
  byType?: Record<string, number>;
  byPriority?: Record<string, number>;
  trends?: {
    daily?: Record<string, number>;
    weekly?: Record<string, number>;
    monthly?: Record<string, number>;
  };
}

// Audit log entry interface
export interface AuditLogEntry {
  id: string;
  entityType: string;
  entityId: string;
  action: string;
  userId?: string;
  changes?: {
    field: string;
    oldValue: unknown;
    newValue: unknown;
  }[];
  metadata?: Record<string, unknown>;
  timestamp: string;
}

// File upload response interface
export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedAt: string;
}

// Bulk operation request interface
export interface BulkOperationRequest<T> {
  operation: 'create' | 'update' | 'delete';
  items: T[];
  options?: {
    continueOnError?: boolean;
    validateOnly?: boolean;
  };
}

// Bulk operation response interface
export interface BulkOperationResponse<T> {
  totalItems: number;
  successCount: number;
  errorCount: number;
  results: {
    success: T[];
    errors: {
      item: unknown;
      error: string;
    }[];
  };
}

// Webhook payload interface
export interface WebhookPayload {
  id: string;
  event: string;
  timestamp: string;
  data: Record<string, unknown>;
  signature?: string;
}

// Rate limit info interface
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  window: number;
}

// Cache info interface
export interface CacheInfo {
  key: string;
  ttl: number;
  size?: number;
  hitCount?: number;
  missCount?: number;
}

// Validation error detail interface
export interface ValidationErrorDetail {
  field: string;
  message: string;
  value?: unknown;
  constraint?: string;
}

// API key info interface
export interface ApiKeyInfo {
  id: string;
  name: string;
  permissions: string[];
  rateLimit?: RateLimitInfo;
  expiresAt?: string;
  lastUsedAt?: string;
}

// System metrics interface
export interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
  };
  uptime: number;
  timestamp: string;
}
