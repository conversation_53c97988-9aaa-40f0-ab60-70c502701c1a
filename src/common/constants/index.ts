// Database constants
export const DATABASE_CONSTANTS = {
  CONNECTION_TIMEOUT: 30000,
  MAX_POOL_SIZE: 10,
  MIN_POOL_SIZE: 2,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// Server constants
export const SERVER_CONSTANTS = {
  DEFAULT_PORT: 3000,
  DEFAULT_HOST: '0.0.0.0',
  REQUEST_TIMEOUT: 30000,
  MAX_PAYLOAD_SIZE: 1048576, // 1MB
} as const;

// API constants
export const API_CONSTANTS = {
  VERSION: 'v1',
  BASE_PATH: '/api',
  HEALTH_PATH: '/health',
  WEBHOOK_PATH: '/webhook',
} as const;

// Pagination constants
export const PAGINATION_CONSTANTS = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
  MIN_LIMIT: 1,
} as const;

// Validation constants
export const VALIDATION_CONSTANTS = {
  MAX_STRING_LENGTH: 1000,
  MAX_DESCRIPTION_LENGTH: 2000,
  MIN_STRING_LENGTH: 1,
  MAX_USER_ID_LENGTH: 100,
  MAX_REQUEST_ID_LENGTH: 100,
} as const;

// Status constants
export const STATUS_CONSTANTS = {
  USER_INCIDENT: {
    PENDING: 'pending',
    IN_PROGRESS: 'in_progress',
    RESOLVED: 'resolved',
    CLOSED: 'closed',
  },
  CROSSCORE: {
    PENDING: 'pending',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
  },
} as const;

// Priority constants
export const PRIORITY_CONSTANTS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
} as const;

// Incident type constants
export const INCIDENT_TYPE_CONSTANTS = {
  IDENTITY_VERIFICATION: 'identity_verification',
  DOCUMENT_VERIFICATION: 'document_verification',
  ADDRESS_VERIFICATION: 'address_verification',
  FRAUD_DETECTION: 'fraud_detection',
  COMPLIANCE_CHECK: 'compliance_check',
  OTHER: 'other',
} as const;

// Verification type constants
export const VERIFICATION_TYPE_CONSTANTS = {
  IDENTITY_VERIFICATION: 'identity_verification',
  DOCUMENT_VERIFICATION: 'document_verification',
  ADDRESS_VERIFICATION: 'address_verification',
  CREDIT_CHECK: 'credit_check',
  FRAUD_CHECK: 'fraud_check',
  KYC_VERIFICATION: 'kyc_verification',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  VALIDATION_ERROR: 'Validation error',
  NOT_FOUND: 'Resource not found',
  INTERNAL_ERROR: 'Internal server error',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  BAD_REQUEST: 'Bad request',
  CONFLICT: 'Resource conflict',
  DATABASE_ERROR: 'Database operation failed',
  EXTERNAL_API_ERROR: 'External API call failed',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  CREATED: 'Resource created successfully',
  UPDATED: 'Resource updated successfully',
  DELETED: 'Resource deleted successfully',
  RETRIEVED: 'Resource retrieved successfully',
  PROCESSED: 'Request processed successfully',
} as const;

// Environment constants
export const ENVIRONMENT_CONSTANTS = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production',
  TEST: 'test',
} as const;

// Log levels
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
  TRACE: 'trace',
} as const;

// HTTP status codes
export const HTTP_STATUS_CODES = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
} as const;

// Date/Time constants
export const DATE_TIME_CONSTANTS = {
  ISO_DATE_FORMAT: 'YYYY-MM-DD',
  ISO_DATETIME_FORMAT: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  TIMEZONE_UTC: 'UTC',
} as const;
