# Service Haven Customer Verification

A Node.js service built with Hapi framework and TypeScript for handling customer verification processes and incident management. This service integrates with <PERSON>perian's CrossCore API for KYC verification and provides comprehensive incident tracking capabilities.

## Features

- **User Incident Management**: Create, track, and manage customer verification incidents
- **CrossCore Integration**: Seamless integration with Experian's CrossCore API for verification services
- **Webhook Support**: Handle real-time notifications from external verification services
- **RESTful API**: Comprehensive REST API with proper validation and error handling
- **TypeScript**: Full TypeScript support for better development experience
- **MongoDB Integration**: Robust data persistence with Mongoose ODM
- **Feature-based Architecture**: Clean, modular code structure with repository pattern

## Architecture

The project follows a feature-based module structure with the repository pattern:

```
src/
├── userIncident/          # User incident management module
│   ├── userIncident.service.ts      # Core business logic
│   ├── userIncident.controller.ts   # HTTP endpoints
│   ├── userIncident.repository.ts   # Database access layer
│   ├── userIncident.schema.ts       # Mongoose schema
│   ├── userIncident.dto.ts         # Data transfer objects
│   └── userIncident.mapper.ts       # DTO to entity mapping
├── crosscore/             # CrossCore/Experian integration module
│   ├── crosscore.service.ts        # Core verification logic
│   ├── crosscore.controller.ts     # HTTP endpoints
│   ├── crosscore.repository.ts     # Database access layer
│   ├── crosscore.schema.ts         # Mongoose schema
│   ├── crosscore.dto.ts           # Data transfer objects
│   └── crosscore.mapper.ts         # DTO to entity mapping
├── webhook/               # Webhook handling module
│   └── webhook.controller.ts       # Webhook endpoints
├── common/                # Shared utilities and types
│   ├── utils/             # Utility functions
│   ├── constants/         # Application constants
│   ├── enums/             # TypeScript enums
│   └── dtos/              # Common data transfer objects
├── routes.ts              # Route configuration
└── server.ts              # Main server setup
```

## Prerequisites

- Node.js (v16 or higher)
- MongoDB (v4.4 or higher)
- npm or yarn package manager

## Installation

1. Clone the repository:

```bash
git clone https://github.com/HavenEngineering/service-haven-customer-verification.git
cd service-haven-customer-verification
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env
# Edit .env file with your configuration
```

4. Start MongoDB (if running locally):

```bash
# Using Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Or start your local MongoDB service
sudo systemctl start mongod
```

## Configuration

Copy `.env.example` to `.env` and configure the following variables:

- `PORT`: Server port (default: 3000)
- `MONGODB_URI`: MongoDB connection string
- `EXPERIAN_API_KEY`: Your Experian API key
- `WEBHOOK_SECRET`: Secret for webhook validation
- Other configuration options as needed

## Usage

### Development

Start the development server with hot reload:

```bash
npm run dev
```

### Production

Build and start the production server:

```bash
npm run build
npm start
```

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build TypeScript to JavaScript
- `npm start` - Start production server
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

## API Documentation

### Health Check

```
GET /health
```

### User Incidents

- `POST /api/v1/incidents` - Create a new incident
- `GET /api/v1/incidents` - Get incidents with filtering and pagination
- `GET /api/v1/incidents/{id}` - Get incident by ID
- `GET /api/v1/users/{userId}/incidents` - Get incidents for a user
- `PUT /api/v1/incidents/{id}` - Update an incident
- `DELETE /api/v1/incidents/{id}` - Delete an incident
- `POST /api/v1/incidents/{id}/assign` - Assign incident to user
- `POST /api/v1/incidents/{id}/resolve` - Resolve an incident
- `GET /api/v1/incidents/statistics` - Get incident statistics

### CrossCore Verification

- `POST /api/v1/crosscore/requests` - Create verification request
- `GET /api/v1/crosscore/requests` - Get requests with filtering
- `GET /api/v1/crosscore/requests/{id}` - Get request by ID
- `GET /api/v1/crosscore/requests/request-id/{requestId}` - Get by request ID
- `GET /api/v1/users/{userId}/crosscore/requests` - Get user requests
- `PUT /api/v1/crosscore/requests/{id}` - Update a request
- `POST /api/v1/crosscore/requests/{id}/cancel` - Cancel a request
- `GET /api/v1/crosscore/pending-webhooks` - Get pending webhooks
- `GET /api/v1/crosscore/statistics` - Get request statistics

### Webhooks

- `POST /webhook/crosscore` - Handle CrossCore webhooks
- `POST /webhook/generic` - Handle generic webhooks
- `GET /webhook/health` - Webhook service health check
- `GET|POST /webhook/validate` - Validate webhook endpoint

## Data Models

### User Incident

```typescript
{
  userId: string;
  incidentType: 'identity_verification' | 'document_verification' | 'address_verification' | 'fraud_detection' | 'compliance_check' | 'other';
  description: string;
  status: 'pending' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedTo?: string;
  metadata?: Record<string, {}>;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
}
```

### CrossCore Request

```typescript
{
  userId: string;
  requestId: string;
  verificationType: 'identity_verification' | 'document_verification' | 'address_verification' | 'credit_check' | 'fraud_check' | 'kyc_verification';
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  requestData: Record<string, {}>;
  responseData?: Record<string, {}>;
  experianRequestId?: string;
  experianStatus?: string;
  errorMessage?: string;
  webhookReceived: boolean;
  webhookData?: Record<string, {}>;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}
```

## Error Handling

The API uses standard HTTP status codes and returns consistent error responses:

```typescript
{
  "error": "Error message",
  "timestamp": "2023-12-07T10:30:00.000Z"
}
```

Common status codes:

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `404` - Not Found
- `500` - Internal Server Error

## Webhook Integration

The service supports webhook notifications from external verification providers:

### CrossCore Webhooks

```typescript
POST /webhook/crosscore
{
  "requestId": "string",
  "experianRequestId": "string",
  "status": "string",
  "data": {},
  "timestamp": "2023-12-07T10:30:00.000Z"
}
```

### Webhook Security

- Validate webhook signatures using `WEBHOOK_SECRET`
- Implement idempotency to handle duplicate webhooks
- Log all webhook events for debugging

## Development

### Code Style

The project uses Prettier for code formatting. Run formatting commands:

```bash
npm run format        # Format all files
npm run format:check  # Check formatting
```

### Project Structure Guidelines

- Each feature module should be self-contained
- Use the repository pattern for data access
- Implement proper error handling and logging
- Write comprehensive validation schemas
- Use TypeScript interfaces for type safety

### Adding New Features

1. Create a new directory under `src/` for the feature
2. Implement the following files:
   - `{feature}.schema.ts` - Mongoose schema
   - `{feature}.dto.ts` - Data transfer objects and validation
   - `{feature}.mapper.ts` - DTO to entity mapping
   - `{feature}.repository.ts` - Database access layer
   - `{feature}.service.ts` - Business logic
   - `{feature}.controller.ts` - HTTP endpoints
3. Register routes in `src/routes.ts`
4. Update documentation

## Testing

```bash
# Run tests (when implemented)
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## Deployment

### Environment Variables

Ensure all required environment variables are set:

- Production MongoDB URI
- Experian API credentials
- Webhook secrets
- Security keys

### Docker Deployment

```bash
# Build Docker image
docker build -t service-haven-customer-verification .

# Run container
docker run -p 3000:3000 --env-file .env service-haven-customer-verification
```

### Health Checks

The service provides health check endpoints:

- `GET /health` - Overall service health
- `GET /webhook/health` - Webhook service health

## Monitoring and Logging

- All requests and responses are logged
- Error tracking with structured logging
- Performance monitoring capabilities
- Database connection health monitoring

## Security Considerations

- Input validation on all endpoints
- Rate limiting (configure as needed)
- Webhook signature validation
- Environment variable protection
- Database connection security

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure code formatting with Prettier
6. Submit a pull request

## License

This project is proprietary software owned by Haven Engineering.

## Support

For support and questions, please contact the development team or create an issue in the repository.
