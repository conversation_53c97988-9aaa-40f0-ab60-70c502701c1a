{"name": "service-haven-customer-verification", "version": "1.0.0", "description": "Service for verifying customers KYC and handle incidents", "main": "dist/server.js", "scripts": {"build": "tsc && cp -r src/generated dist/", "start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "test": "echo \"Error: no test specified\" && exit 1", "type-check": "tsc", "lint": "eslint --fix 'src/**/*.ts'"}, "keywords": ["hapi", "typescript", "customer-verification", "kyc", "crosscore"], "author": "", "license": "ISC", "dependencies": {"@hapi/hapi": "^21.4.0", "@hapi/joi": "^17.1.1", "@prisma/client": "^6.10.1", "@types/uuid": "^10.0.0", "dotenv": "^16.5.0", "mongoose": "^8.16.0", "pg": "^8.16.1", "prisma": "^6.10.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/hapi__hapi": "^21.0.0", "@types/hapi__joi": "^17.1.15", "@types/node": "^24.0.3", "@types/pg": "^8.15.4", "eslint": "^9.29.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1"}}